DDMControl = require("DDMControlV2")
local 作者UUID = "5d34a272-d237-4c96-b715-19846928e16d"--替换成自己的
local 卡密UUID  ="a48e380e-a75d-4bec-bf7d-791e2d3c5d4b"--替换成自己的
local 卡密密钥 = "a15056a9-e7ad-4eb0-94ce-23977fe86273"--替换成自己的
local 云控UUID = "1ebcb45f2fb4490ab2aae651b929b299" --创建项目会生成
local 服务器地址 = "api.privateapi.xyz"

arr = jsonLib.decode(getUIConfig("醉月脚本.config"))

local 卡密  = arr["page2"]["输入卡密"]--替换成自己的卡密


local 网络验证失败次数 = 0
local 最大失败次数 = 3 -- 最大重试次数
--ocal 本地验证状态 = false -- 本地状态
function 检查网络连接()
	
	local ret, code = httpGet("http://www.baidu.com") -- 发送 GET 请求
	return code == 200 -- 如果返回状态码是 200，表示连接成功
	
end

function 初始化叮当猫rg()
	
	local 初始化结果 = DDMControl.初始化(服务器地址, "9000", 云控UUID, 作者UUID, 云UIUUID)
	
end





function 叮当猫初始化km()
	
	local 初始化结果km = DDMControl.初始化(服务器地址, "9000", "", 作者UUID, "")
	
end





function 心跳回调(msg)
	print(msg)
	if msg.code == 1 then
		print("心跳成功")
		网络验证失败次数 = 0
	elseif msg.code == -5 then
		print("请重新登录,一般是卡密被禁用,删除,设备被解绑!")
		toast("请重新登录,一般是卡密被禁用,删除,设备被解绑!")
		sleep(2000)
		exitScript()
	elseif msg.code == -8 then
		print("卡密到期")
		toast("卡密到期")
		sleep(2000)
		exitScript()
	elseif msg.code == -9999 then
		print("心跳失败,网络错误!")
		网络验证失败次数 = 网络验证失败次数 + 1
		if 网络验证失败次数 >= 最大失败次数 then
			print("网络验证失败，进入离线模式")
			-- 这里可以设置一些离线模式的逻辑
		end
	elseif msg.code == -11 then
		print("未知错误!", msg.msg)
		toast("错误原因:" .. msg.msg, 0, 0, 12)
	elseif msg.code == -6666 then
		print("有人尝试破解卡密系统!", msg.cdkey)
		exitScript()
	else
		print("未知错误!", msg.msg)
		toast("错误原因:" .. msg.msg, 0, 0, 12)
		exitScript()
	end
end

function 卡密登录(登录的卡密)
	
	
	local 登录结果 = DDMControl.卡密_卡密登录(卡密UUID, 卡密密钥, 登录的卡密, 心跳回调, true, true, 60)
	print(登录结果)
	
	if 登录结果.code == 0 then
		print("卡密被禁用")
		toast("卡密被禁用", 0, 0, 12)
		sleep(2000)
		exitScript()
	elseif 登录结果.code == -1 then
		print("网络错误,请检查网络!")
		-- toast("网络错误,请检查网络!", 0, 0, 12)
		当前重试次数 = 当前重试次数 + 1
		sleep(2000) -- 等待一段时间后重试
	elseif 登录结果.code == 1 then
		print("卡密登录成功!")
		toast("登录成功,到期时间:" .. 登录结果.endTime, 0, 0, 12)
		sleep(2000)
		return -- 登录成功，退出重试循环
	elseif 登录结果.code == -9 then
		print("卡密授权窗口达到上限,登录失败!")
		toast("卡密授权窗口达到上限,登录失败!", 0, 0, 12)
		sleep(2000)
		exitScript()
	elseif 登录结果.code == -7 then
		print("卡密过期!")
		toast("卡密过期!", 0, 0, 12)
		sleep(2000)
		exitScript()
	else
		print("未知错误!", 登录结果.msg)
		toast(登录结果.msg, 0, 0, 12)
		sleep(2000)
		exitScript()
	end
end


function 功能代码()
	while 1 do
		sleep(500)
		争霸boss()
		杀戮()
		--杀戮战场地图手动()
		--华山()
		世界boss封装()
		神秘()
		
		
		挂星图()
		
		
	end
end
codebaidu=0
codeDDM=0

function 脚本开始函数()
	
	for i=1,2 do
		sleep(1000)
		print(i)
		retbaidu, codebaidu = httpGet("http://www.baidu.com",10) -- 发送 GET 请求
		retDDM, codeDDM = httpGet("http://api.privateapi.xyz:9000",10)
		if codebaidu==200 and  codeDDM==200 then
			break
		end
	end
	if codebaidu~=200 then
		exitScript()
		
	end
	
	if codebaidu==200 and  codeDDM==200 then
		DDMControl = require("DDMControlV2")
		初始化叮当猫rg()
		DDMControl.云控_连接云控系统()
		叮当猫初始化km()
		local 更新结果 = DDMControl.热更新_检测更新并直接更新(60, true)
		卡密登录(卡密) ---下面写你自己的代码
		功能代码()
		
	elseif codebaidu==200 and  codeDDM~=200 then
		功能代码()
		
	end
end







