#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的代码空行清理工具 - 删除所有单独的空行
"""

import os

def clean_empty_lines(file_path):
    """删除文件中的所有单独空行"""
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 统计原始空行数
        original_lines = content.split('\n')
        empty_lines_count = sum(1 for line in original_lines if line.strip() == '')

        if empty_lines_count == 0:
            print(f"⏭️  {os.path.basename(file_path)}: 无空行需要清理")
            return False

        # 简单粗暴：删除所有单独的空行
        lines = content.split('\n')
        new_lines = [line for line in lines if line.strip() != '']

        # 写回文件
        new_content = '\n'.join(new_lines)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)

        removed = len(lines) - len(new_lines)
        print(f"✅ {os.path.basename(file_path)}: 删除了 {removed} 行空行")
        return True

    except Exception as e:
        print(f"❌ {os.path.basename(file_path)}: 处理失败 - {e}")
        return False

def main():
    print("🚀 代码空行清理工具")
    print("=" * 40)

    # 让用户选择处理方式
    print("请选择处理方式:")
    print("1. 清理当前目录下所有 .lua 文件")
    print("2. 清理当前目录下所有代码文件 (.lua, .py, .js, .cpp, .c, .h)")
    print("3. 清理指定文件")
    print("4. 清理指定目录")

    choice = input("\n请输入选择 (1-4): ").strip()

    files_to_process = []

    if choice == "1":
        # 只处理 .lua 文件
        for root, _, files in os.walk('.'):
            for file in files:
                if file.endswith('.lua'):
                    files_to_process.append(os.path.join(root, file))


    elif choice == "2":
        # 处理多种代码文件
        code_extensions = ['.lua', '.py', '.js', '.cpp', '.c', '.h', '.java', '.cs', '.php']
        for root, _, files in os.walk('.'):
            for file in files:
                if any(file.endswith(ext) for ext in code_extensions):
                    files_to_process.append(os.path.join(root, file))

    elif choice == "3":
        # 处理指定文件
        file_path = input("请输入文件路径: ").strip()
        if os.path.exists(file_path):
            files_to_process.append(file_path)
        else:
            print(f"❌ 文件不存在: {file_path}")
            return

    elif choice == "4":
        # 处理指定目录
        dir_path = input("请输入目录路径: ").strip()
        if not os.path.exists(dir_path):
            print(f"❌ 目录不存在: {dir_path}")
            return

        file_pattern = input("请输入文件扩展名 (如 .lua): ").strip()
        for root, _, files in os.walk(dir_path):
            for file in files:
                if file.endswith(file_pattern):
                    files_to_process.append(os.path.join(root, file))

    else:
        print("❌ 无效选择")
        return

    if not files_to_process:
        print("⚠️  没有找到需要处理的文件")
        return

    print(f"\n📊 找到 {len(files_to_process)} 个文件")
    print()

    # 处理每个文件
    modified_count = 0
    for file_path in files_to_process:
        if clean_empty_lines(file_path):
            modified_count += 1

    print()
    print("=" * 40)
    print("✨ 处理完成！")
    print(f"📊 统计: 处理了 {len(files_to_process)} 个文件，修改了 {modified_count} 个文件")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
